# RevenueCat Integration for Whitetail LIVN

This document explains the RevenueCat integration for mobile subscription management while maintaining the existing web Stripe implementation.

## Overview

The app now supports dual payment systems:
- **Mobile (iOS/Android)**: RevenueCat with App Store/Google Play subscriptions
- **Web**: Existing Stripe checkout system

## Architecture

### Platform Detection
The system automatically detects the platform and uses the appropriate payment method:
- `shouldUseRevenueCat()` returns `true` on native mobile platforms
- Web users continue to use Stripe checkout

### Key Components

1. **`useRevenueCat` Hook** (`src/hooks/useRevenueCat.ts`)
   - Manages RevenueCat initialization and state
   - Handles purchases and subscription status
   - Provides product information

2. **`RevenueCatContext`** (`src/contexts/RevenueCatContext.tsx`)
   - App-wide RevenueCat state management
   - Syncs subscription status with Supabase
   - Provides utility functions

3. **Enhanced `ServicesPromotionCard`** (`src/components/ServicesPromotionCard.tsx`)
   - Platform-aware UI
   - Shows different pricing and buttons based on platform
   - Handles both RevenueCat and Stripe purchases

## Product Configuration

### Product ID
- **Annual Subscription**: `com.whitetaillivn.whitetaillivn.Annual`
- **Pricing**: $99/year (50% discount from $199)
- **Features**: Premium services promotion for one year

### RevenueCat Setup Required

**Important**: You need to configure RevenueCat with your actual API keys:

1. **Update API Keys** in `src/hooks/useRevenueCat.ts`:
   ```typescript
   const REVENUECAT_API_KEY_ANDROID = 'goog_your_android_key_here';
   const REVENUECAT_API_KEY_IOS = 'appl_your_ios_key_here';
   ```

2. **Configure Products** in RevenueCat Dashboard:
   - Create product with ID: `com.whitetaillivn.whitetaillivn.Annual`
   - Set up offerings and packages
   - Configure App Store Connect and Google Play Console

## Features Implemented

### Mobile Features
- ✅ **Automatic subscription detection**
- ✅ **Native purchase flow** through app stores
- ✅ **Purchase restoration**
- ✅ **Subscription status sync** with Supabase
- ✅ **Platform-specific pricing** display
- ✅ **Error handling** for purchase failures

### Web Features
- ✅ **Existing Stripe integration** maintained
- ✅ **Seamless fallback** for web users
- ✅ **Consistent UI** across platforms

## User Experience

### Mobile Users
1. See native pricing from app stores
2. Purchase through App Store/Google Play
3. Subscription automatically syncs with profile
4. Can restore purchases if needed

### Web Users
1. See web pricing ($99/year)
2. Redirected to Stripe checkout
3. Existing flow unchanged

## Testing

### Test Component
Access `/revenuecat-test` to test RevenueCat functionality:
- View initialization status
- Test purchase flow
- Check subscription status
- Debug product information

### Testing Checklist
- [ ] RevenueCat initializes on mobile
- [ ] Product information loads correctly
- [ ] Purchase flow works (sandbox)
- [ ] Subscription status syncs with Supabase
- [ ] Restore purchases works
- [ ] Web fallback works correctly

## Implementation Details

### Subscription Status Sync
When a user successfully subscribes on mobile:
1. RevenueCat confirms the purchase
2. App updates local subscription state
3. Supabase profile is updated with:
   - `is_promoted: true`
   - `promoted_until: [1 year from now]`

### Error Handling
- Purchase cancellation handled gracefully
- Network errors show appropriate messages
- Initialization failures fall back to web flow

### Platform-Specific UI
The ServicesPromotionCard shows:
- **Mobile**: RevenueCat pricing, native purchase button, restore button
- **Web**: Stripe pricing, checkout redirect button
- **Subscription Active**: Success message with current status

## File Structure

```
src/
├── hooks/
│   └── useRevenueCat.ts              # RevenueCat hook
├── contexts/
│   └── RevenueCatContext.tsx         # Context provider
├── components/
│   ├── ServicesPromotionCard.tsx     # Enhanced promotion card
│   └── RevenueCatTest.tsx           # Test component
└── pages/
    └── ServicesDirectory.tsx         # Uses the promotion card
```

## Configuration Steps

### 1. RevenueCat Dashboard
1. Create project for iOS and Android
2. Add product: `com.whitetaillivn.whitetaillivn.Annual`
3. Configure offerings and packages
4. Get API keys for both platforms

### 2. App Store Connect (iOS)
1. Create in-app purchase product
2. Set product ID: `com.whitetaillivn.whitetaillivn.Annual`
3. Configure pricing and availability
4. Submit for review

### 3. Google Play Console (Android)
1. Create subscription product
2. Set product ID: `com.whitetaillivn.whitetaillivn.Annual`
3. Configure pricing and availability
4. Publish to production

### 4. Update Code
1. Replace placeholder API keys in `useRevenueCat.ts`
2. Test with sandbox accounts
3. Deploy to production

## Troubleshooting

### Common Issues

1. **RevenueCat not initializing**
   - Check API keys are correct
   - Verify product ID matches exactly
   - Check network connectivity

2. **Purchase fails**
   - Ensure sandbox testing is enabled
   - Check App Store/Play Console configuration
   - Verify product is available in user's region

3. **Subscription not syncing**
   - Check Supabase connection
   - Verify user authentication
   - Check error logs in console

### Debug Tools
- Use `/revenuecat-test` page for debugging
- Check browser console for errors
- Monitor RevenueCat dashboard for events

## Next Steps

1. **Configure RevenueCat** with actual API keys and products
2. **Test thoroughly** on both iOS and Android devices
3. **Submit app store products** for review
4. **Monitor subscription metrics** in RevenueCat dashboard
5. **Add analytics** for purchase funnel tracking

## Security Notes

- API keys are client-side visible (normal for RevenueCat)
- Server-side validation handled by RevenueCat
- Supabase sync provides additional verification
- Use sandbox environment for testing
