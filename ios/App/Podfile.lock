PODS:
  - Capacitor (7.3.0):
    - Capacitor<PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorCordova (7.3.0)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorGeolocation (7.1.2):
    - Capacitor
    - IONGeolocationLib (~> 1.0)
  - CapacitorPluginSafeArea (4.0.0):
    - Capacitor
  - CapacitorShare (7.0.1):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapacitorVoiceRecorder (7.0.6):
    - Capacitor
  - IONGeolocationLib (1.0.0)
  - PurchasesHybridCommon (15.0.0):
    - RevenueCat (= 5.33.0)
  - RevenueCat (5.33.0)
  - RevenuecatPurchasesCapacitor (11.0.0):
    - Capacitor
    - PurchasesHybridCommon (= 15.0.0)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - CapacitorPluginSafeArea (from `../../node_modules/capacitor-plugin-safe-area`)
  - "CapacitorShare (from `../../node_modules/@capacitor/share`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - CapacitorVoiceRecorder (from `../../node_modules/capacitor-voice-recorder`)
  - "RevenuecatPurchasesCapacitor (from `../../node_modules/@revenuecat/purchases-capacitor`)"

SPEC REPOS:
  trunk:
    - IONGeolocationLib
    - PurchasesHybridCommon
    - RevenueCat

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorPluginSafeArea:
    :path: "../../node_modules/capacitor-plugin-safe-area"
  CapacitorShare:
    :path: "../../node_modules/@capacitor/share"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorVoiceRecorder:
    :path: "../../node_modules/capacitor-voice-recorder"
  RevenuecatPurchasesCapacitor:
    :path: "../../node_modules/@revenuecat/purchases-capacitor"

SPEC CHECKSUMS:
  Capacitor: fbd134fa28e503720559ecddb5ab6b41d69de347
  CapacitorApp: d63334c052278caf5d81585d80b21905c6f93f39
  CapacitorCordova: 2685f5c43675793b5f06dfd66b3b26268f003b97
  CapacitorDevice: fe3f190e1d718f4607bdc6b73993433d1c84f409
  CapacitorGeolocation: 8c86a3afea574c84447c17905da96f2356ad913e
  CapacitorPluginSafeArea: af80830df43015a915e504a56318a3027d3fc806
  CapacitorShare: 58d6c2da63b093e8693287b2d36db92435538435
  CapacitorSplashScreen: 19cd3573e57507e02d6f34597a8c421e00931487
  CapacitorVoiceRecorder: b136a5989c9a306d57f0c9fc530be79a06ccf7b2
  IONGeolocationLib: 81f33f88d025846946de2cf63b0c7628e7c6bc9d
  PurchasesHybridCommon: 18b65ad813f50460d4af7e033abb5cc44a686a6f
  RevenueCat: 9fd6fc0ea8a22fd9d11242feb298f3bb6f9f86db
  RevenuecatPurchasesCapacitor: 1ee681cd7bf4bcf19aa597df23ffe1ba60245bdd

PODFILE CHECKSUM: 6dabe899bfa497361fa3f36ce9ace1d1c3617acd

COCOAPODS: 1.15.2
