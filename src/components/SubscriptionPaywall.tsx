import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useRevenueCatContext, useSubscriptionOfferings } from '@/contexts/RevenueCatContext';
import { PurchasesPackage } from '@revenuecat/purchases-capacitor';
import { toast } from 'sonner';
import { 
  Crown, 
  Check, 
  Loader2, 
  Star, 
  Zap, 
  Shield, 
  Smartphone,
  RefreshCw
} from 'lucide-react';

interface SubscriptionPaywallProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  features?: string[];
}

const SubscriptionPaywall: React.FC<SubscriptionPaywallProps> = ({
  isOpen,
  onClose,
  title = "Upgrade to Premium",
  description = "Unlock all premium features and get the most out of Whitetail LIVN",
  features = [
    "Unlimited video uploads",
    "Advanced hunting analytics",
    "Premium sound library",
    "Ad-free experience",
    "Priority customer support",
    "Exclusive hunting content"
  ]
}) => {
  const { 
    purchasePackage, 
    restorePurchases, 
    isLoading, 
    subscriptionStatus,
    isNative 
  } = useRevenueCatContext();
  
  const { 
    monthlyPackage, 
    yearlyPackage, 
    availablePackages,
    isLoading: offeringsLoading 
  } = useSubscriptionOfferings();

  const [selectedPackage, setSelectedPackage] = useState<PurchasesPackage | null>(
    yearlyPackage || monthlyPackage || null
  );

  const handlePurchase = async () => {
    if (!selectedPackage) {
      toast.error('Please select a subscription plan');
      return;
    }

    if (!isNative) {
      toast.info('Subscriptions are only available on mobile devices');
      return;
    }

    const result = await purchasePackage(selectedPackage);
    if (result) {
      onClose();
      toast.success('Welcome to Premium! 🎉');
    }
  };

  const handleRestore = async () => {
    if (!isNative) {
      toast.info('Restore purchases is only available on mobile devices');
      return;
    }

    const result = await restorePurchases();
    if (result) {
      toast.success('Purchases restored successfully!');
      onClose();
    }
  };

  const formatPrice = (pkg: PurchasesPackage) => {
    return pkg.storeProduct.priceString;
  };

  const getPackageDescription = (pkg: PurchasesPackage) => {
    if (pkg.packageType === 'ANNUAL' || pkg.identifier.includes('yearly')) {
      return 'Best Value - Save 50%';
    }
    if (pkg.packageType === 'MONTHLY' || pkg.identifier.includes('monthly')) {
      return 'Most Popular';
    }
    return '';
  };

  const getPackagePeriod = (pkg: PurchasesPackage) => {
    if (pkg.packageType === 'ANNUAL' || pkg.identifier.includes('yearly')) {
      return 'per year';
    }
    if (pkg.packageType === 'MONTHLY' || pkg.identifier.includes('monthly')) {
      return 'per month';
    }
    if (pkg.packageType === 'WEEKLY' || pkg.identifier.includes('weekly')) {
      return 'per week';
    }
    return '';
  };

  if (subscriptionStatus.isActive) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              Premium Active
            </DialogTitle>
            <DialogDescription>
              You already have an active premium subscription!
            </DialogDescription>
          </DialogHeader>
          <div className="text-center py-4">
            <div className="text-green-600 mb-2">
              <Check className="h-12 w-12 mx-auto" />
            </div>
            <p className="text-sm text-muted-foreground">
              Expires: {subscriptionStatus.expirationDate ? 
                new Date(subscriptionStatus.expirationDate).toLocaleDateString() : 
                'Never'
              }
            </p>
          </div>
          <Button onClick={onClose} className="w-full">
            Continue
          </Button>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Crown className="h-6 w-6 text-yellow-500" />
            {title}
          </DialogTitle>
          <DialogDescription className="text-base">
            {description}
          </DialogDescription>
        </DialogHeader>

        {/* Features List */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 my-4">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </div>

        {/* Subscription Plans */}
        {offeringsLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading subscription options...</span>
          </div>
        ) : availablePackages.length > 0 ? (
          <div className="space-y-3">
            <h3 className="font-semibold text-center mb-4">Choose Your Plan</h3>
            {availablePackages.map((pkg) => (
              <Card 
                key={pkg.identifier}
                className={`cursor-pointer transition-all ${
                  selectedPackage?.identifier === pkg.identifier 
                    ? 'ring-2 ring-primary border-primary' 
                    : 'hover:border-primary/50'
                }`}
                onClick={() => setSelectedPackage(pkg)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-semibold capitalize">
                          {pkg.storeProduct.title}
                        </h4>
                        {getPackageDescription(pkg) && (
                          <Badge variant="secondary" className="text-xs">
                            {getPackageDescription(pkg)}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {getPackagePeriod(pkg)}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">
                        {formatPrice(pkg)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              {isNative 
                ? 'No subscription plans available at the moment'
                : 'Subscriptions are only available on mobile devices'
              }
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 mt-6">
          <Button 
            onClick={handlePurchase}
            disabled={!selectedPackage || isLoading || !isNative}
            className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Crown className="mr-2 h-4 w-4" />
                Subscribe Now
              </>
            )}
          </Button>

          <Button 
            variant="outline" 
            onClick={handleRestore}
            disabled={isLoading || !isNative}
            className="w-full"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Restore Purchases
          </Button>
        </div>

        {/* Terms */}
        <div className="text-xs text-muted-foreground text-center mt-4 space-y-1">
          <p>Subscription automatically renews unless cancelled.</p>
          <p>Cancel anytime in your device settings.</p>
          <div className="flex justify-center gap-4 mt-2">
            <button className="underline hover:no-underline">
              Terms of Service
            </button>
            <button className="underline hover:no-underline">
              Privacy Policy
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SubscriptionPaywall;
