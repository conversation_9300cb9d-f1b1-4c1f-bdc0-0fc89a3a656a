import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Crown, Check } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
const YEARLY_OPTION = {
  id: "yearly",
  priceId: "price_1RjhctF1bISMFshisXLZHRMG",
  name: "Yearly Plan",
  price: "$99.00",
  originalPrice: "$199.00",
  duration: "/year",
  features: ["Reach Serious Whitetail Hunters Only", "Get in Front of Buyers, Not Browsers", "Generate Direct Leads Instantly", "Stand Out at the Top of Searches", "Be Part of the Hunting Community, Not Just Another Ad", "Turn Exposure Into Bookings and Sales", "Build Brand Loyalty with Real Outdoorsmen"]
};
const ServicesPromotionCard = () => {
  const [loading, setLoading] = useState(false);
  const handlePromoteServices = async () => {
    try {
      setLoading(true);
      const {
        data,
        error
      } = await supabase.functions.invoke('create-promotion-checkout', {
        body: {
          priceId: YEARLY_OPTION.priceId
        }
      });
      if (error) {
        console.error('Error creating checkout:', error);
        toast.error("Failed to create checkout session");
        return;
      }
      if (data?.url) {
        // Open Stripe checkout in a new tab
        window.open(data.url, '_blank');
      } else {
        toast.error("No checkout URL received");
      }
    } catch (error) {
      console.error('Error in handlePromoteServices:', error);
      toast.error("Failed to start promotion checkout");
    } finally {
      setLoading(false);
    }
  };
  return <Card className="bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Crown className="h-5 w-5 text-yellow-600" />
          <CardTitle className="text-lg font-semibold text-yellow-800">
            Promote Your Services
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-yellow-700">Promote Your Services and put your business front and center. Gain priority placement, reach more hunters, and turn views into bookings. It's time to stand out—let us help you get there.</p>

        <div className="rounded-lg border border-yellow-400 bg-yellow-50 p-4">
          <div className="flex items-center justify-between mb-3">
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-yellow-800">{YEARLY_OPTION.name}</span>
                <span className="px-2 py-1 text-xs font-medium bg-red-200 text-red-800 rounded-full">
                  Early Launch Discount
                </span>
              </div>
              <div className="text-sm text-yellow-600 mt-1">
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{YEARLY_OPTION.price}</span>
                  <span className="text-xs line-through text-gray-500">{YEARLY_OPTION.originalPrice}</span>
                  <span>{YEARLY_OPTION.duration}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            {YEARLY_OPTION.features.map((feature, index) => <div key={index} className="flex items-center gap-2 text-sm text-yellow-600">
                <Check className="h-4 w-4" />
                <span>{feature}</span>
              </div>)}
          </div>
        </div>

        <Button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white" size="sm" onClick={handlePromoteServices} disabled={loading}>
          {loading ? "Creating checkout..." : "Promote Services - $99/year"}
        </Button>
      </CardContent>
    </Card>;
};
export default ServicesPromotionCard;