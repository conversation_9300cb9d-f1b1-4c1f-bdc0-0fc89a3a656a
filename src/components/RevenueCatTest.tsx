import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useRevenueCatContext, useSubscriptionOfferings } from '@/contexts/RevenueCatContext';
import SubscriptionPaywall from './SubscriptionPaywall';
import PremiumFeature, { PremiumVideoUpload, PremiumAnalytics, PremiumStatusIndicator } from './PremiumFeature';
import { 
  Crown, 
  Smartphone, 
  RefreshCw, 
  Info, 
  CheckCircle, 
  XCircle,
  Loader2,
  Package,
  User
} from 'lucide-react';

/**
 * Test component for RevenueCat integration
 * Helps debug and test subscription functionality
 */
const RevenueCatTest: React.FC = () => {
  const {
    isInitialized,
    customerInfo,
    subscriptionStatus,
    isLoading,
    error,
    restorePurchases,
    refreshCustomerInfo,
    isNative
  } = useRevenueCatContext();

  const {
    offerings,
    availablePackages,
    monthlyPackage,
    yearlyPackage,
    isLoading: offeringsLoading
  } = useSubscriptionOfferings();

  const [showPaywall, setShowPaywall] = useState(false);

  const handleRefresh = async () => {
    await refreshCustomerInfo();
  };

  const handleRestore = async () => {
    await restorePurchases();
  };

  return (
    <div className="p-4 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            RevenueCat Integration Test
          </CardTitle>
          <CardDescription>
            Test and debug RevenueCat subscription functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <span className="font-medium">Platform:</span>
              <Badge variant={isNative ? "default" : "secondary"}>
                {isNative ? "Native" : "Web"}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium">Initialized:</span>
              {isInitialized ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium">Premium:</span>
              <Badge variant={subscriptionStatus.isActive ? "default" : "secondary"}>
                {subscriptionStatus.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Customer Info */}
          {customerInfo && (
            <div className="space-y-2">
              <h4 className="font-medium">Customer Information:</h4>
              <div className="bg-muted p-3 rounded-md text-sm space-y-1">
                <div>User ID: {customerInfo.originalAppUserId}</div>
                <div>First Seen: {new Date(customerInfo.firstSeen).toLocaleDateString()}</div>
                <div>Active Entitlements: {Object.keys(customerInfo.entitlements.active).length}</div>
                <div>All Purchases: {Object.keys(customerInfo.allPurchaseDates).length}</div>
              </div>
            </div>
          )}

          {/* Subscription Status */}
          {subscriptionStatus.isActive && (
            <div className="space-y-2">
              <h4 className="font-medium">Subscription Details:</h4>
              <div className="bg-green-50 p-3 rounded-md text-sm space-y-1">
                <div>Product ID: {subscriptionStatus.productId}</div>
                <div>Will Renew: {subscriptionStatus.willRenew ? 'Yes' : 'No'}</div>
                <div>Grace Period: {subscriptionStatus.isInGracePeriod ? 'Yes' : 'No'}</div>
                <div>Intro Offer: {subscriptionStatus.isInIntroOfferPeriod ? 'Yes' : 'No'}</div>
                {subscriptionStatus.expirationDate && (
                  <div>Expires: {new Date(subscriptionStatus.expirationDate).toLocaleString()}</div>
                )}
              </div>
            </div>
          )}

          {/* Offerings */}
          <div className="space-y-2">
            <h4 className="font-medium">Available Offerings:</h4>
            {offeringsLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading offerings...</span>
              </div>
            ) : offerings.length > 0 ? (
              <div className="space-y-2">
                {offerings.map((offering, index) => (
                  <div key={offering.identifier} className="bg-muted p-3 rounded-md text-sm">
                    <div className="font-medium">{offering.identifier}</div>
                    <div>Packages: {offering.availablePackages.length}</div>
                    <div className="mt-2 space-y-1">
                      {offering.availablePackages.map((pkg) => (
                        <div key={pkg.identifier} className="ml-4 text-xs">
                          • {pkg.storeProduct.title} - {pkg.storeProduct.priceString}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No offerings available</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={handleRefresh}
              disabled={isLoading || !isInitialized}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Info
            </Button>

            <Button 
              onClick={handleRestore}
              disabled={isLoading || !isNative}
              variant="outline"
              size="sm"
            >
              <Package className="mr-2 h-4 w-4" />
              Restore Purchases
            </Button>

            <Button 
              onClick={() => setShowPaywall(true)}
              disabled={!isNative}
              size="sm"
            >
              <Crown className="mr-2 h-4 w-4" />
              Show Paywall
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Premium Status Indicator */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Premium Status</CardTitle>
        </CardHeader>
        <CardContent>
          <PremiumStatusIndicator />
        </CardContent>
      </Card>

      {/* Premium Feature Examples */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <PremiumVideoUpload currentUploads={5} maxFreeUploads={3}>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Video Upload</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This would be the video upload interface for premium users.
              </p>
            </CardContent>
          </Card>
        </PremiumVideoUpload>

        <PremiumAnalytics>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Advanced Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Premium analytics dashboard would appear here.
              </p>
            </CardContent>
          </Card>
        </PremiumAnalytics>
      </div>

      {/* Generic Premium Feature */}
      <PremiumFeature
        featureName="test feature"
        title="Test Premium Feature"
        description="This is a test of the premium feature gating system."
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Premium Content</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This content is only visible to premium subscribers.
            </p>
          </CardContent>
        </Card>
      </PremiumFeature>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted p-3 rounded-md text-xs font-mono">
            <div>Is Native: {isNative.toString()}</div>
            <div>Is Initialized: {isInitialized.toString()}</div>
            <div>Is Loading: {isLoading.toString()}</div>
            <div>Has Error: {(!!error).toString()}</div>
            <div>Offerings Count: {offerings.length}</div>
            <div>Available Packages: {availablePackages.length}</div>
            <div>Monthly Package: {monthlyPackage ? 'Available' : 'Not Available'}</div>
            <div>Yearly Package: {yearlyPackage ? 'Available' : 'Not Available'}</div>
          </div>
        </CardContent>
      </Card>

      {/* Paywall */}
      <SubscriptionPaywall 
        isOpen={showPaywall}
        onClose={() => setShowPaywall(false)}
      />
    </div>
  );
};

export default RevenueCatTest;
