import React, { createContext, useContext, useEffect } from 'react';
import { useRevenueCat, RevenueCatConfig, SubscriptionStatus } from '@/hooks/useRevenueCat';
import { CustomerInfo, PurchasesOffering, PurchasesPackage } from '@revenuecat/purchases-capacitor';
import { useAuth } from '@/contexts/AuthContext';

interface RevenueCatContextType {
  // State
  isInitialized: boolean;
  customerInfo: CustomerInfo | null;
  offerings: PurchasesOffering[];
  subscriptionStatus: SubscriptionStatus;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  purchasePackage: (packageToPurchase: PurchasesPackage) => Promise<CustomerInfo | null>;
  restorePurchases: () => Promise<CustomerInfo | null>;
  refreshCustomerInfo: () => Promise<CustomerInfo | null>;
  setUserId: (userId: string) => Promise<void>;
  logOut: () => Promise<void>;
  loadOfferings: () => Promise<void>;
  
  // Utilities
  isNative: boolean;
  isPremium: boolean;
  hasActiveSubscription: boolean;
}

const RevenueCatContext = createContext<RevenueCatContextType | undefined>(undefined);

interface RevenueCatProviderProps {
  children: React.ReactNode;
  config?: Partial<RevenueCatConfig>;
}

export const RevenueCatProvider: React.FC<RevenueCatProviderProps> = ({ 
  children, 
  config = {} 
}) => {
  const { user } = useAuth();
  
  // Default configuration - you should replace these with your actual RevenueCat API keys
  const defaultConfig: RevenueCatConfig = {
    apiKey: process.env.REACT_APP_REVENUECAT_API_KEY || 'your_revenuecat_api_key_here',
    appUserId: user?.id,
    observerMode: false,
    useAmazon: false,
    ...config
  };

  const revenueCatHook = useRevenueCat(defaultConfig);

  // Sync user ID with RevenueCat when user changes
  useEffect(() => {
    if (revenueCatHook.isInitialized && user?.id) {
      revenueCatHook.setUserId(user.id);
    }
  }, [revenueCatHook.isInitialized, user?.id, revenueCatHook.setUserId]);

  // Log out from RevenueCat when user logs out
  useEffect(() => {
    if (revenueCatHook.isInitialized && !user) {
      revenueCatHook.logOut();
    }
  }, [revenueCatHook.isInitialized, user, revenueCatHook.logOut]);

  // Derived state
  const isPremium = revenueCatHook.subscriptionStatus.isActive;
  const hasActiveSubscription = revenueCatHook.subscriptionStatus.isActive;

  const contextValue: RevenueCatContextType = {
    ...revenueCatHook,
    isPremium,
    hasActiveSubscription
  };

  return (
    <RevenueCatContext.Provider value={contextValue}>
      {children}
    </RevenueCatContext.Provider>
  );
};

export const useRevenueCatContext = () => {
  const context = useContext(RevenueCatContext);
  if (context === undefined) {
    throw new Error('useRevenueCatContext must be used within a RevenueCatProvider');
  }
  return context;
};

// Utility hooks for common use cases
export const usePremiumFeatures = () => {
  const { isPremium, subscriptionStatus } = useRevenueCatContext();
  
  return {
    isPremium,
    canAccessFeature: (featureName: string) => {
      // You can implement feature-specific logic here
      console.log(`Checking access to feature: ${featureName}`);
      return isPremium;
    },
    subscriptionStatus
  };
};

export const useSubscriptionOfferings = () => {
  const { offerings, isLoading, loadOfferings } = useRevenueCatContext();
  
  // Get the current offering (usually the first one)
  const currentOffering = offerings.length > 0 ? offerings[0] : null;
  
  // Get available packages
  const availablePackages = currentOffering?.availablePackages || [];
  
  // Common package types
  const monthlyPackage = availablePackages.find(pkg => 
    pkg.packageType === 'MONTHLY' || pkg.identifier.includes('monthly')
  );
  
  const yearlyPackage = availablePackages.find(pkg => 
    pkg.packageType === 'ANNUAL' || pkg.identifier.includes('yearly') || pkg.identifier.includes('annual')
  );
  
  const weeklyPackage = availablePackages.find(pkg => 
    pkg.packageType === 'WEEKLY' || pkg.identifier.includes('weekly')
  );

  return {
    offerings,
    currentOffering,
    availablePackages,
    monthlyPackage,
    yearlyPackage,
    weeklyPackage,
    isLoading,
    loadOfferings
  };
};

// Premium feature gate component
interface PremiumGateProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  featureName?: string;
}

export const PremiumGate: React.FC<PremiumGateProps> = ({ 
  children, 
  fallback = null, 
  featureName = 'premium feature' 
}) => {
  const { canAccessFeature } = usePremiumFeatures();
  
  if (canAccessFeature(featureName)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
};
