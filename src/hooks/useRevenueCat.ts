import { useEffect, useState, useCallback } from 'react';
import { Purchases, CustomerInfo, PurchasesOffering, PurchasesPackage, LOG_LEVEL } from '@revenuecat/purchases-capacitor';
import { Capacitor } from '@capacitor/core';
import { toast } from 'sonner';

export interface RevenueCatConfig {
  apiKey: string;
  appUserId?: string;
  observerMode?: boolean;
  userDefaultsSuiteName?: string;
  useAmazon?: boolean;
}

export interface SubscriptionStatus {
  isActive: boolean;
  productId?: string;
  expirationDate?: string;
  willRenew?: boolean;
  isInGracePeriod?: boolean;
  isInIntroOfferPeriod?: boolean;
}

/**
 * Custom hook for RevenueCat integration
 * Handles initialization, purchases, subscriptions, and customer info
 */
export const useRevenueCat = (config: RevenueCatConfig) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [offerings, setOfferings] = useState<PurchasesOffering[]>([]);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus>({
    isActive: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize RevenueCat
  const initialize = useCallback(async () => {
    if (!Capacitor.isNativePlatform()) {
      console.warn('RevenueCat only works on native platforms');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Configure RevenueCat
      await Purchases.setLogLevel({ level: LOG_LEVEL.DEBUG });
      
      // Initialize with API key
      await Purchases.configure({
        apiKey: config.apiKey,
        appUserId: config.appUserId,
        observerMode: config.observerMode || false,
        userDefaultsSuiteName: config.userDefaultsSuiteName,
        useAmazon: config.useAmazon || false
      });

      // Set up customer info listener
      await Purchases.addCustomerInfoUpdateListener((info) => {
        console.log('Customer info updated:', info);
        setCustomerInfo(info.customerInfo);
        updateSubscriptionStatus(info.customerInfo);
      });

      // Get initial customer info
      const { customerInfo: initialInfo } = await Purchases.getCustomerInfo();
      setCustomerInfo(initialInfo);
      updateSubscriptionStatus(initialInfo);

      // Get offerings
      await loadOfferings();

      setIsInitialized(true);
      console.log('RevenueCat initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize RevenueCat';
      setError(errorMessage);
      console.error('RevenueCat initialization error:', err);
      toast.error(`RevenueCat Error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  }, [config]);

  // Load available offerings
  const loadOfferings = useCallback(async () => {
    try {
      const { offerings: offeringsData } = await Purchases.getOfferings();
      setOfferings(offeringsData.all);
      console.log('Loaded offerings:', offeringsData.all);
    } catch (err) {
      console.error('Error loading offerings:', err);
      toast.error('Failed to load subscription options');
    }
  }, []);

  // Update subscription status from customer info
  const updateSubscriptionStatus = useCallback((info: CustomerInfo) => {
    const activeEntitlements = info.entitlements.active;
    const hasActiveSubscription = Object.keys(activeEntitlements).length > 0;

    if (hasActiveSubscription) {
      const firstEntitlement = Object.values(activeEntitlements)[0];
      setSubscriptionStatus({
        isActive: true,
        productId: firstEntitlement.productIdentifier,
        expirationDate: firstEntitlement.expirationDate,
        willRenew: firstEntitlement.willRenew,
        isInGracePeriod: firstEntitlement.isInGracePeriod,
        isInIntroOfferPeriod: firstEntitlement.isInIntroOfferPeriod
      });
    } else {
      setSubscriptionStatus({ isActive: false });
    }
  }, []);

  // Purchase a package
  const purchasePackage = useCallback(async (packageToPurchase: PurchasesPackage) => {
    if (!isInitialized) {
      toast.error('RevenueCat not initialized');
      return null;
    }

    try {
      setIsLoading(true);
      setError(null);

      const { customerInfo: updatedInfo } = await Purchases.purchasePackage({
        aPackage: packageToPurchase
      });

      setCustomerInfo(updatedInfo);
      updateSubscriptionStatus(updatedInfo);
      
      toast.success('Purchase successful!');
      return updatedInfo;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Purchase failed';
      setError(errorMessage);
      console.error('Purchase error:', err);
      toast.error(`Purchase failed: ${errorMessage}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, updateSubscriptionStatus]);

  // Restore purchases
  const restorePurchases = useCallback(async () => {
    if (!isInitialized) {
      toast.error('RevenueCat not initialized');
      return null;
    }

    try {
      setIsLoading(true);
      setError(null);

      const { customerInfo: restoredInfo } = await Purchases.restorePurchases();
      
      setCustomerInfo(restoredInfo);
      updateSubscriptionStatus(restoredInfo);
      
      toast.success('Purchases restored successfully!');
      return restoredInfo;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to restore purchases';
      setError(errorMessage);
      console.error('Restore purchases error:', err);
      toast.error(`Restore failed: ${errorMessage}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, updateSubscriptionStatus]);

  // Get customer info
  const refreshCustomerInfo = useCallback(async () => {
    if (!isInitialized) return null;

    try {
      const { customerInfo: refreshedInfo } = await Purchases.getCustomerInfo();
      setCustomerInfo(refreshedInfo);
      updateSubscriptionStatus(refreshedInfo);
      return refreshedInfo;
    } catch (err) {
      console.error('Error refreshing customer info:', err);
      return null;
    }
  }, [isInitialized, updateSubscriptionStatus]);

  // Set user ID
  const setUserId = useCallback(async (userId: string) => {
    if (!isInitialized) return;

    try {
      const { customerInfo: updatedInfo } = await Purchases.logIn({ appUserId: userId });
      setCustomerInfo(updatedInfo);
      updateSubscriptionStatus(updatedInfo);
      console.log('User ID set successfully:', userId);
    } catch (err) {
      console.error('Error setting user ID:', err);
      toast.error('Failed to set user ID');
    }
  }, [isInitialized, updateSubscriptionStatus]);

  // Log out user
  const logOut = useCallback(async () => {
    if (!isInitialized) return;

    try {
      const { customerInfo: updatedInfo } = await Purchases.logOut();
      setCustomerInfo(updatedInfo);
      updateSubscriptionStatus(updatedInfo);
      console.log('User logged out successfully');
    } catch (err) {
      console.error('Error logging out:', err);
      toast.error('Failed to log out');
    }
  }, [isInitialized, updateSubscriptionStatus]);

  // Initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  return {
    // State
    isInitialized,
    customerInfo,
    offerings,
    subscriptionStatus,
    isLoading,
    error,
    
    // Actions
    purchasePackage,
    restorePurchases,
    refreshCustomerInfo,
    setUserId,
    logOut,
    loadOfferings,
    
    // Utilities
    isNative: Capacitor.isNativePlatform()
  };
};
