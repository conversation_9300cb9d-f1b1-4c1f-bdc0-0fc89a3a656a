import { useState, useEffect, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';
import { Purchases, PurchasesOffering, PurchasesPackage, CustomerInfo } from '@revenuecat/purchases-capacitor';
import { toast } from 'sonner';

export interface RevenueCatProduct {
  identifier: string;
  title: string;
  description: string;
  price: string;
  priceString: string;
  currencyCode: string;
  originalPrice?: string;
  discountPercentage?: number;
}

export interface RevenueCatState {
  isInitialized: boolean;
  isLoading: boolean;
  customerInfo: CustomerInfo | null;
  currentOffering: PurchasesOffering | null;
  annualPackage: PurchasesPackage | null;
  isSubscribed: boolean;
  error: string | null;
}

const REVENUECAT_API_KEY_ANDROID = 'goog_your_android_key_here'; // Replace with your actual Android key
const REVENUECAT_API_KEY_IOS = 'appl_djVrNehRbBRpGIJKNWkuDcPIDyv'; // Replace with your actual iOS key
const ANNUAL_PRODUCT_ID = 'com.whitetaillivn.whitetaillivn.Annual';

export const useRevenueCat = () => {
  const [state, setState] = useState<RevenueCatState>({
    isInitialized: false,
    isLoading: true,
    customerInfo: null,
    currentOffering: null,
    annualPackage: null,
    isSubscribed: false,
    error: null,
  });

  // Initialize RevenueCat
  const initializeRevenueCat = useCallback(async () => {
    if (!Capacitor.isNativePlatform()) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'RevenueCat only works on native platforms'
      }));
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Get the appropriate API key based on platform
      const platform = Capacitor.getPlatform();
      const apiKey = platform === 'ios' ? REVENUECAT_API_KEY_IOS : REVENUECAT_API_KEY_ANDROID;

      // Configure RevenueCat
      await Purchases.configure({
        apiKey,
        appUserID: null, // Let RevenueCat generate anonymous user ID app13e21e4a0a
      });

      console.log('RevenueCat initialized successfully');

      // Get customer info
      const res = await Purchases.getCustomerInfo();
      
      // Get current offerings
      const offerings = await Purchases.getOfferings();
      const currentOffering = offerings.current;
      
      // Find the annual package
      let annualPackage: PurchasesPackage | null = null;
      if (currentOffering) {
        annualPackage = currentOffering.availablePackages.find(
          pkg => pkg.product.identifier === ANNUAL_PRODUCT_ID
        ) || currentOffering.annual || null;
      }

      // Check subscription status
      const isSubscribed = Object.keys(res.customerInfo.entitlements.active).length > 0;

      setState(prev => ({
        ...prev,
        isInitialized: true,
        isLoading: false,
        customerInfo: res.customerInfo,
        currentOffering,
        annualPackage,
        isSubscribed,
        error: null,
      }));

      console.log('RevenueCat state updated:', {
        isSubscribed,
        hasOffering: !!currentOffering,
        hasAnnualPackage: !!annualPackage,
      });

    } catch (error) {
      console.error('Error initializing RevenueCat:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to initialize RevenueCat',
      }));
    }
  }, []);

  // Purchase the annual subscription
  const purchaseAnnualSubscription = useCallback(async (): Promise<boolean> => {
    if (!state.annualPackage) {
      toast.error('Annual subscription not available');
      return false;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const purchaseResult = await Purchases.purchasePackage({
        aPackage: state.annualPackage,
      });

      // Update customer info
      const updatedCustomerInfo = purchaseResult.customerInfo;
      const isSubscribed = Object.keys(updatedCustomerInfo.entitlements.active).length > 0;

      setState(prev => ({
        ...prev,
        isLoading: false,
        customerInfo: updatedCustomerInfo,
        isSubscribed,
      }));

      if (isSubscribed) {
        toast.success('Subscription activated successfully!');
        return true;
      } else {
        toast.error('Subscription activation failed');
        return false;
      }

    } catch (error) {
      console.error('Error purchasing subscription:', error);
      setState(prev => ({ ...prev, isLoading: false }));
      
      // Handle specific error cases
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as any).code;
        if (errorCode === 'PURCHASE_CANCELLED') {
          toast.info('Purchase cancelled');
        } else if (errorCode === 'PURCHASE_NOT_ALLOWED') {
          toast.error('Purchase not allowed');
        } else {
          toast.error('Purchase failed. Please try again.');
        }
      } else {
        toast.error('Purchase failed. Please try again.');
      }
      
      return false;
    }
  }, [state.annualPackage]);

  // Restore purchases
  const restorePurchases = useCallback(async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const res = await Purchases.restorePurchases();
      const isSubscribed = Object.keys(res.customerInfo.entitlements.active).length > 0;

      setState(prev => ({
        ...prev,
        isLoading: false,
        customerInfo: res.customerInfo,
        isSubscribed,
      }));

      if (isSubscribed) {
        toast.success('Purchases restored successfully!');
        return true;
      } else {
        toast.info('No active subscriptions found');
        return false;
      }

    } catch (error) {
      console.error('Error restoring purchases:', error);
      setState(prev => ({ ...prev, isLoading: false }));
      toast.error('Failed to restore purchases');
      return false;
    }
  }, []);

  // Get formatted product information
  const getProductInfo = useCallback((): RevenueCatProduct | null => {
    if (!state.annualPackage) return null;

    const product = state.annualPackage.product;
    
    // Calculate discount percentage (assuming original price is $199)
    const originalPrice = 199.00;
    const currentPrice = parseFloat(product.price.toString());
    const discountPercentage = Math.round(((originalPrice - currentPrice) / originalPrice) * 100);

    return {
      identifier: product.identifier,
      title: product.title || 'Annual Subscription',
      description: product.description || 'Premium services promotion for one year',
      price: product.price.toString(),
      priceString: product.priceString,
      currencyCode: product.currencyCode || 'USD',
      originalPrice: `$${originalPrice.toFixed(2)}`,
      discountPercentage,
    };
  }, [state.annualPackage]);

  // Initialize on mount
  useEffect(() => {
    initializeRevenueCat();
  }, [initializeRevenueCat]);

  return {
    ...state,
    purchaseAnnualSubscription,
    restorePurchases,
    getProductInfo,
    reinitialize: initializeRevenueCat,
  };
};
