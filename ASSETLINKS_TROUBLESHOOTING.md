# Android App Links - Assetlinks.json Troubleshooting Guide

## Current Status
✅ **Fixed**: Created properly formatted `public/.well-known/assetlinks.json`
✅ **Validated**: JSON format is correct
✅ **Certificate**: Using debug certificate fingerprint

## Issues and Solutions

### 1. **Domain Ownership Verification**

**Problem**: "Domain ownership not verified"
**Solution**: Ensure your domain serves the assetlinks.json file correctly

**Test your domain**:
```bash
curl -I https://whitetaillivn.com/.well-known/assetlinks.json
```

**Expected response**:
- Status: `200 OK`
- Content-Type: `application/json`
- File should be accessible without redirects

### 2. **Assetlinks JSON File Validation**

**Problem**: "Assetlinks JSON file failed to validate"
**Current file location**: `public/.well-known/assetlinks.json`

**Validation checklist**:
- ✅ Valid JSON format
- ✅ Correct package name: `com.whitetaillivn.whitetaillivn`
- ✅ Proper relation: `delegate_permission/common.handle_all_urls`
- ⚠️  Certificate fingerprint needs verification

### 3. **Certificate Fingerprint Issues**

**Current fingerprint** (Debug):
```
94:FE:19:B7:18:29:77:25:83:78:BD:85:D8:39:3F:CC:AF:27:06:FF:06:50:72:2C:9D:AC:1F:F8:E0:B0:5D:A5
```

**For Production**, you need the **Play Console Upload Certificate** fingerprint:

1. Go to Play Console → Your App → Setup → App Integrity
2. Copy the **SHA-256 certificate fingerprint** from "Upload key certificate"
3. Add it to assetlinks.json

### 4. **Web Server Configuration**

**Ensure your web server**:
- Serves `.well-known` directory
- Returns correct MIME type: `application/json`
- No authentication required
- HTTPS enabled

**For Vite/React apps**, add to `vite.config.ts`:
```typescript
export default defineConfig({
  // ... other config
  publicDir: 'public', // Ensures .well-known is served
})
```

## Step-by-Step Fix

### Step 1: Get Production Certificate Fingerprint
1. Open Google Play Console
2. Go to your app → Setup → App Integrity
3. Find "Upload key certificate" section
4. Copy the SHA-256 fingerprint

### Step 2: Update assetlinks.json
Replace the current fingerprint with your production certificate:

```json
[
  {
    "relation": ["delegate_permission/common.handle_all_urls"],
    "target": {
      "namespace": "android_app",
      "package_name": "com.whitetaillivn.whitetaillivn",
      "sha256_cert_fingerprints": [
        "YOUR_PRODUCTION_CERTIFICATE_FINGERPRINT_HERE"
      ]
    }
  }
]
```

### Step 3: Deploy and Test
1. Deploy your website with the updated assetlinks.json
2. Test the URL: `https://whitetaillivn.com/.well-known/assetlinks.json`
3. Validate with Google's tool: https://developers.google.com/digital-asset-links/tools/generator

### Step 4: Verify in Play Console
1. Go to Play Console → Your App → Setup → App Integrity
2. Click "Link and verify" next to your domain
3. Wait for verification (can take up to 24 hours)

## Testing Commands

**Test local file**:
```bash
# Validate JSON format
python3 -m json.tool public/.well-known/assetlinks.json

# Check if file exists locally
ls -la public/.well-known/assetlinks.json
```

**Test deployed file**:
```bash
# Test accessibility
curl -v https://whitetaillivn.com/.well-known/assetlinks.json

# Check headers
curl -I https://whitetaillivn.com/.well-known/assetlinks.json
```

**Validate with Google's tool**:
- Visit: https://developers.google.com/digital-asset-links/tools/generator
- Enter your domain and package name
- Verify the generated statement matches your file

## Common Issues

### Issue 1: 404 Not Found
**Cause**: Web server not serving .well-known directory
**Fix**: Ensure your hosting platform serves static files from public/.well-known/

### Issue 2: Wrong MIME Type
**Cause**: Server returns text/plain instead of application/json
**Fix**: Configure server to return application/json for .json files

### Issue 3: Certificate Mismatch
**Cause**: Using debug certificate instead of production certificate
**Fix**: Get production certificate from Play Console and update assetlinks.json

### Issue 4: Domain Not Verified
**Cause**: Google can't access the file or domain ownership not proven
**Fix**: Ensure HTTPS is working and file is publicly accessible

## Next Steps

1. **Get production certificate** from Play Console
2. **Update assetlinks.json** with production fingerprint
3. **Deploy to production** (ensure HTTPS works)
4. **Test the URL** manually
5. **Re-verify in Play Console**
6. **Wait up to 24 hours** for verification

## Verification URLs

- **Your assetlinks.json**: https://whitetaillivn.com/.well-known/assetlinks.json
- **Google's validator**: https://developers.google.com/digital-asset-links/tools/generator
- **Play Console**: App → Setup → App Integrity → Domain verification

## Support

If issues persist:
1. Check Play Console error messages
2. Use Google's Digital Asset Links API for testing
3. Verify your domain's SSL certificate is valid
4. Ensure no redirects are happening to the assetlinks.json file
